'use client'

import {useSelectedHighlight} from '@/context/SelectedHighlightContext'
import {useSelectedLeague} from '@/context/SelectedLeagueContext'
import {useSelectedSport} from '@/context/SelectedSportContext'
import {useQuery} from '@tanstack/react-query'
import {useRouter} from 'next/navigation'
import YouTube, {YouTubeEvent} from 'react-youtube'
import Error from '../Shared/Error'
import {leagueToDisplayName} from '../Shared/Util'
import styles from './Highlight.module.css'
import HighlightLoading from './HighlightLoading'

export default function Highlight() {
    const {selectedHighlight} = useSelectedHighlight()
    const {selectedSport} = useSelectedSport()
    const {selectedLeague} = useSelectedLeague()
    const router = useRouter()

    const handleBackClick = () => {
        // First try to use the stored referrer URL
        const referrerUrl = sessionStorage.getItem('highlightReferrer')
        console.log(`[BackClick] Referrer URL: ${referrerUrl}`)
        if (referrerUrl) {
            sessionStorage.removeItem('highlightReferrer')
            router.push(referrerUrl)
            return
        }

        // Fallback: Use browser's native back functionality
        if (window.history.length > 1) {
            window.history.back()
        } else {
            // Last resort: construct the correct back URL based on context
            let backUrl = `/${selectedSport}`
            if (selectedLeague && selectedLeague !== 'all') {
                backUrl += `/${selectedLeague}`
            }
            router.push(backUrl)
        }
    }

    const fetchHighlights = async () => {
        // Use cached API endpoint instead of direct Supabase query
        const response = await fetch(`/api/cached-highlight?label=${selectedHighlight}`)

        if (!response.ok) {
            const error = new globalThis.Error('Failed to fetch highlight')
            throw error
        }

        const result = await response.json()
        return result.highlight || []
    }

    const {data, isLoading, error} = useQuery({
        queryKey: ['highlights', selectedHighlight],
        queryFn: fetchHighlights,
        staleTime: 1000 * 60 * 5,
        enabled: !!selectedHighlight,
    })

    if (isLoading) {
        return <HighlightLoading />
    }

    if (error) {
        return (
            <Error
                title="Sorry, it's not you, it's us"
                message="We're having trouble loading this highlight right now. Please try again later."
            />
        )
    }

    if (data?.length === 0) {
        return (
            <Error
                title='No highlight found'
                message='It does not look like there is a highlight with this title.'
            />
        )
    }

    const opts = {
        width: '100%',
        height: 'auto',
        playerVars: {
            autoplay: 1,
            showinfo: 0,
            cc_load_policy: 0,
            rel: 0,
            modestbranding: 1,
            loop: 1,
            iv_load_policy: 3,
            playsinline: 1,
        },
    }

    const handleVideoReady = (event: YouTubeEvent) => {
        event.target.playVideo()
    }

    return (
        <div className={styles.highlightContainer}>
            {data?.map((highlight: any) => (
                <div key={highlight.id}>
                    <button className={styles.highlightBackButton} onClick={handleBackClick}>
                        Back
                    </button>
                    <ul className={styles.highlightItem}>
                        <li>
                            <YouTube
                                videoId={highlight.video_url.split('v=')[1]}
                                opts={opts}
                                onReady={handleVideoReady}
                                iframeClassName={styles.highlightVideoIframe}
                            />
                        </li>
                        <li className={styles.highlightTitle}>
                            <h2>
                                {highlight.name} - {leagueToDisplayName(highlight.league)}{' '}
                                {highlight.season}
                            </h2>
                        </li>
                    </ul>
                </div>
            ))}
        </div>
    )
}
