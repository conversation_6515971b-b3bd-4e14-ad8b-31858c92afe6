'use client'

import {useSelectedHighlight} from '@/context/SelectedHighlightContext'
import {useQuery} from '@tanstack/react-query'
import YouTube, {YouTubeEvent} from 'react-youtube'
import Error from '../Shared/Error'
import {leagueToDisplayName} from '../Shared/Util'
import styles from './Highlight.module.css'
import HighlightLoading from './HighlightLoading'

export default function Highlight() {
    const {selectedHighlight} = useSelectedHighlight()

    const handleBackClick = () => {
        // Use browser's native back functionality for proper history navigation
        window.history.back()
    }

    const fetchHighlights = async () => {
        // Use cached API endpoint instead of direct Supabase query
        const response = await fetch(`/api/cached-highlight?label=${selectedHighlight}`)

        if (!response.ok) {
            const error = new globalThis.Error('Failed to fetch highlight')
            throw error
        }

        const result = await response.json()
        return result.highlight || []
    }

    const {data, isLoading, error} = useQuery({
        queryKey: ['highlights', selectedHighlight],
        queryFn: fetchHighlights,
        staleTime: 1000 * 60 * 5,
        enabled: !!selectedHighlight,
    })

    if (isLoading) {
        return <HighlightLoading />
    }

    if (error) {
        return (
            <Error
                title="Sorry, it's not you, it's us"
                message="We're having trouble loading this highlight right now. Please try again later."
            />
        )
    }

    if (data?.length === 0) {
        return (
            <Error
                title='No highlight found'
                message='It does not look like there is a highlight with this title.'
            />
        )
    }

    const opts = {
        width: '100%',
        height: 'auto',
        playerVars: {
            autoplay: 1,
            showinfo: 0,
            cc_load_policy: 0,
            rel: 0,
            modestbranding: 1,
            loop: 1,
            iv_load_policy: 3,
            playsinline: 1,
        },
    }

    const handleVideoReady = (event: YouTubeEvent) => {
        event.target.playVideo()
    }

    return (
        <div className={styles.highlightContainer}>
            {data?.map((highlight: any) => (
                <div key={highlight.id}>
                    <button className={styles.highlightBackButton} onClick={handleBackClick}>
                        Back
                    </button>
                    <ul className={styles.highlightItem}>
                        <li>
                            <YouTube
                                videoId={highlight.video_url.split('v=')[1]}
                                opts={opts}
                                onReady={handleVideoReady}
                                iframeClassName={styles.highlightVideoIframe}
                            />
                        </li>
                        <li className={styles.highlightTitle}>
                            <h2>
                                {highlight.name} - {leagueToDisplayName(highlight.league)}{' '}
                                {highlight.season}
                            </h2>
                        </li>
                    </ul>
                </div>
            ))}
        </div>
    )
}
