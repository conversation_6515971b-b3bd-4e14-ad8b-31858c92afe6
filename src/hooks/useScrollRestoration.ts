'use client'

import {usePathname} from 'next/navigation'
import {useEffect} from 'react'

export const useScrollRestoration = () => {
    const pathname = usePathname()

    useEffect(() => {
        // Restore scroll position when component mounts
        const restoreScrollPosition = () => {
            if (pathname) {
                const savedScrollPosition = sessionStorage.getItem(`scrollPosition_${pathname}`)
                if (savedScrollPosition) {
                    const scrollY = parseInt(savedScrollPosition, 10)
                    if (!isNaN(scrollY) && scrollY > 0) {
                        // Use setTimeout to ensure DOM is fully rendered
                        setTimeout(() => {
                            window.scrollTo({
                                top: scrollY,
                                behavior: 'instant',
                            })
                            // Clean up the stored position after restoring
                            sessionStorage.removeItem(`scrollPosition_${pathname}`)
                        }, 150) // Increased timeout for better reliability
                    }
                }
            }
        }

        restoreScrollPosition()
    }, [pathname])

    // Store current scroll position before navigating away
    useEffect(() => {
        const storeScrollPosition = () => {
            if (pathname && window.scrollY > 0) {
                sessionStorage.setItem(`scrollPosition_${pathname}`, window.scrollY.toString())
            }
        }

        // Store scroll position on page unload
        const handleBeforeUnload = () => {
            storeScrollPosition()
        }

        // Store scroll position on visibility change (mobile browser behavior)
        const handleVisibilityChange = () => {
            if (document.visibilityState === 'hidden') {
                storeScrollPosition()
            }
        }

        window.addEventListener('beforeunload', handleBeforeUnload)
        document.addEventListener('visibilitychange', handleVisibilityChange)

        // Store scroll position when component unmounts
        return () => {
            window.removeEventListener('beforeunload', handleBeforeUnload)
            document.removeEventListener('visibilitychange', handleVisibilityChange)
            storeScrollPosition()
        }
    }, [pathname])
}
