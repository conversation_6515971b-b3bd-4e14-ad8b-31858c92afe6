import Footer from '@/components/Footer/Footer'
import {Leagues} from '@/components/Leagues/Leagues'
import StructuredData from '@/components/SEO/StructuredData'
import {PaginationProvider} from '@/context/PaginationContext'
import {ReactQueryProvider} from '@/context/ReactQueryProvider'
import {SelectedHighlightProvider} from '@/context/SelectedHighlightContext'
import {SelectedLeagueProvider} from '@/context/SelectedLeagueContext'
import {SelectedSportProvider} from '@/context/SelectedSportContext'
import {metadataForLayoutPage} from '@/utils/metadata'
import type {Metadata, Viewport} from 'next'
import './globals.css'

export const metadata: Metadata = {
    ...metadataForLayoutPage,
}
export const viewport: Viewport = {
    themeColor: '#ff4b44',
}

export default function RootLayout({
    children,
}: Readonly<{
    children: React.ReactNode
}>) {
    return (
        <html lang='en'>
            <head>
                <meta name='viewport' content='width=device-width, initial-scale=1' />
                <meta name='theme-color' content='#ff4b44' />
                <meta name='color-scheme' content='dark' />

                <link rel='preconnect' href='https://fonts.googleapis.com' />
                <link rel='preconnect' href='https://fonts.gstatic.com' crossOrigin='anonymous' />
                <link rel='preconnect' href='https://img.youtube.com' />
                <link rel='preconnect' href='https://i.ytimg.com' />
                <link rel='preconnect' href='https://cdn.counter.dev' />
                <link rel='preconnect' href='https://www.youtube.com' />

                <link rel='preload' href='/other/header.png' as='image' />

                {/* DNS prefetch for external resources */}
                <link rel='dns-prefetch' href='//youtube.com' />
                <link rel='dns-prefetch' href='//youtu.be' />
                <link rel='dns-prefetch' href='//i.ytimg.com' />
                <link rel='dns-prefetch' href='//img.youtube.com' />
                <link rel='dns-prefetch' href='//cdn.counter.dev' />

                {/* Preconnect to critical resources */}
                <link rel='preconnect' href='//i.ytimg.com' crossOrigin='anonymous' />
                <link rel='preconnect' href='//img.youtube.com' crossOrigin='anonymous' />

                {/* Resource hints for fonts */}
                <link
                    rel='preload'
                    href='https://fonts.googleapis.com/css2?family=Inter+Tight:ital,wght@0,100..900;1,100..900&family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Teko:wght@300..700&display=swap'
                    as='style'
                />
                <link
                    rel='stylesheet'
                    href='https://fonts.googleapis.com/css2?family=Inter+Tight:ital,wght@0,100..900;1,100..900&family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Teko:wght@300..700&display=swap'
                />
            </head>
            <body>
                <StructuredData type='organization' />
                <ReactQueryProvider>
                    <SelectedSportProvider>
                        <SelectedLeagueProvider>
                            <PaginationProvider>
                                <SelectedHighlightProvider>
                                    <header>
                                        <a href='/'>
                                            <h1>Thepelota</h1>
                                        </a>
                                    </header>
                                    <main>
                                        <aside>
                                            <Leagues />
                                        </aside>
                                        <section>
                                            {children}
                                            <Footer />
                                        </section>
                                    </main>
                                </SelectedHighlightProvider>
                            </PaginationProvider>
                        </SelectedLeagueProvider>
                    </SelectedSportProvider>
                </ReactQueryProvider>
                <script
                    src='https://cdn.counter.dev/script.js'
                    data-id='616fbf43-c49c-4275-9d43-00cc083e6c17'
                    data-utcoffset='-8'
                    async
                    defer
                ></script>
            </body>
        </html>
    )
}
