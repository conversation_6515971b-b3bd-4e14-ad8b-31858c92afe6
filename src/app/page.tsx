import Highlights from '@/components/Highlights/Highlights'
import HighlightsLoading from '@/components/Highlights/HighlightsLoading'
import {metadataForHomePage} from '@/utils/metadata'
import {Metadata} from 'next'
import {Suspense} from 'react'

export const metadata: Metadata = {
    ...metadataForHomePage,
}

function HomeComponent() {
    return (
        <Suspense fallback={<HighlightsLoading />}>
            <Highlights />
        </Suspense>
    )
}

export default async function Page() {
    return <HomeComponent />
}
