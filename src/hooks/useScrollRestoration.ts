'use client'

import {usePathname} from 'next/navigation'
import {useEffect, useRef} from 'react'

export const useScrollRestoration = () => {
    const pathname = usePathname()
    const isRestoringRef = useRef(false)

    useEffect(() => {
        // Restore scroll position when component mounts
        const restoreScrollPosition = () => {
            if (pathname && !isRestoringRef.current) {
                const savedScrollPosition = sessionStorage.getItem(`scrollPosition_${pathname}`)
                if (savedScrollPosition) {
                    const scrollY = parseInt(savedScrollPosition, 10)
                    console.log(`[ScrollRestore] Restoring scroll to ${scrollY} for ${pathname}`)
                    if (!isNaN(scrollY) && scrollY > 0) {
                        isRestoringRef.current = true

                        // Use multiple timeouts to ensure DOM is fully rendered
                        const attemptRestore = (attempt = 0) => {
                            if (attempt < 5) {
                                // Max 5 attempts
                                setTimeout(
                                    () => {
                                        const currentHeight = document.documentElement.scrollHeight
                                        if (currentHeight > scrollY || attempt >= 4) {
                                            window.scrollTo({
                                                top: scrollY,
                                                behavior: 'instant',
                                            })
                                            // Clean up the stored position after restoring
                                            sessionStorage.removeItem(`scrollPosition_${pathname}`)
                                            isRestoringRef.current = false
                                        } else {
                                            attemptRestore(attempt + 1)
                                        }
                                    },
                                    100 + attempt * 50
                                ) // Increasing delay
                            }
                        }

                        attemptRestore()
                    }
                }
            }
        }

        restoreScrollPosition()
    }, [pathname])

    // Store current scroll position before navigating away
    useEffect(() => {
        const storeScrollPosition = () => {
            if (pathname && window.scrollY > 0) {
                sessionStorage.setItem(`scrollPosition_${pathname}`, window.scrollY.toString())
            }
        }

        // Store scroll position on page unload
        const handleBeforeUnload = () => {
            storeScrollPosition()
        }

        // Store scroll position on visibility change (mobile browser behavior)
        const handleVisibilityChange = () => {
            if (document.visibilityState === 'hidden') {
                storeScrollPosition()
            }
        }

        // Handle browser back/forward navigation
        const handlePopState = () => {
            // Small delay to let the new page load before restoring scroll
            setTimeout(() => {
                const newPathname = window.location.pathname
                const savedScrollPosition = sessionStorage.getItem(`scrollPosition_${newPathname}`)
                if (savedScrollPosition) {
                    const scrollY = parseInt(savedScrollPosition, 10)
                    if (!isNaN(scrollY) && scrollY > 0) {
                        window.scrollTo({
                            top: scrollY,
                            behavior: 'instant',
                        })
                        sessionStorage.removeItem(`scrollPosition_${newPathname}`)
                    }
                }
            }, 100)
        }

        window.addEventListener('beforeunload', handleBeforeUnload)
        document.addEventListener('visibilitychange', handleVisibilityChange)
        window.addEventListener('popstate', handlePopState)

        // Store scroll position when component unmounts
        return () => {
            window.removeEventListener('beforeunload', handleBeforeUnload)
            document.removeEventListener('visibilitychange', handleVisibilityChange)
            window.removeEventListener('popstate', handlePopState)
            storeScrollPosition()
        }
    }, [pathname])
}
