import Highlights from '@/components/Highlights/Highlights'
import HighlightsLoading from '@/components/Highlights/HighlightsLoading'
import StructuredData from '@/components/SEO/StructuredData'
import {metadataForHomePage} from '@/utils/metadata'
import {Metadata} from 'next'
import {Suspense} from 'react'

type Props = {
    params: Promise<{
        sport: string
    }>
    searchParams: Promise<{[key: string]: string | string[] | undefined}>
}

export const metadata: Metadata = {
    ...metadataForHomePage,
}

export default async function Page({params}: Props) {
    const {sport} = await params

    return (
        <>
            <StructuredData type='sport' data={{sport}} />
            <Suspense fallback={<HighlightsLoading />}>
                <Highlights />
            </Suspense>
        </>
    )
}
