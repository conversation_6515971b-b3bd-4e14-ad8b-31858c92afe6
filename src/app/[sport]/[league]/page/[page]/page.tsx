import Highlights from '@/components/Highlights/Highlights'
import HighlightsLoading from '@/components/Highlights/HighlightsLoading'
import {leagues} from '@/types/league'
import {LEAGUEDESCRIPTION, metadataForSportAndLeaguePages} from '@/utils/metadata'
import {Metadata} from 'next'
import {Suspense} from 'react'

export async function generateMetadata({
    params,
}: {
    params: Promise<{sport: string; league: string; page: string}>
}): Promise<Metadata> {
    const {sport, league, page} = await params
    const sportLeagues = leagues[sport] || []
    const leagueInfo = sportLeagues.find((l) => l.label === league)
    const leagueName = leagueInfo?.displayName || league

    return metadataForSportAndLeaguePages({
        title: `${leagueName} Highlights - Page ${page}`,
        description: LEAGUEDESCRIPTION(leagueName),
        url: `https://thepelota.tv/${sport}/${league}/page/${page}`,
    })
}

export default function Page() {
    return (
        <Suspense fallback={<HighlightsLoading />}>
            <Highlights />
        </Suspense>
    )
}
